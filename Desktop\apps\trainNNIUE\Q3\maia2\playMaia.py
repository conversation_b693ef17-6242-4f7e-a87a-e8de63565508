#!/usr/bin/env python3
"""
MAIA2 Model 8-bit Quantization Script

This script quantizes the MAIA2 chess model to 8-bit precision using PyTorch's
built-in quantization methods. It supports both static and dynamic quantization.

Usage:
    python quantize_maia2.py --model_type rapid --quantization_method dynamic --output_dir ./quantized_models
"""

import argparse
import os
import time
import torch
import torch.quantization as quantization
from torch.quantization import prepare, convert
import pandas as pd
from tqdm import tqdm
import warnings
warnings.filterwarnings("ignore")

# Import MAIA2 modules (assuming they're in the same directory or installed)
try:
    from model import from_pretrained
    from inference import prepare, inference_each, TestDataset, get_preds
    from dataset import load_example_test_dataset
    from utils import get_all_possible_moves, create_elo_dict
except ImportError:
    print("Warning: Could not import MAIA2 modules. Make sure they are in the Python path.")
    print("You may need to adjust the import statements based on your project structure.")


class QuantizedMAIA2Model(torch.nn.Module):
    """Wrapper class for quantized MAIA2 model with proper forward method."""
    
    def __init__(self, original_model):
        super().__init__()
        self.model = original_model
    
    def forward(self, boards, elos_self, elos_oppo):
        return self.model(boards, elos_self, elos_oppo)


def calibration_data_loader(model_type="rapid", num_samples=100, batch_size=32):
    """
    Create a calibration data loader for static quantization.
    
    Args:
        model_type: Type of model ("rapid" or "blitz")
        num_samples: Number of samples to use for calibration
        batch_size: Batch size for calibration
    
    Returns:
        DataLoader for calibration data
    """
    print(f"Loading calibration data for {model_type} model...")
    
    try:
        # Load example test dataset for calibration
        data = load_example_test_dataset()
        data = data.head(num_samples)  # Use only specified number of samples
        
        all_moves_dict = {move: i for i, move in enumerate(get_all_possible_moves())}
        elo_dict = create_elo_dict()
        
        dataset = TestDataset(data, all_moves_dict, elo_dict)
        dataloader = torch.utils.data.DataLoader(
            dataset, 
            batch_size=batch_size, 
            shuffle=False, 
            drop_last=False,
            num_workers=0  # Use 0 for calibration to avoid multiprocessing issues
        )
        
        print(f"Created calibration dataloader with {len(dataset)} samples")
        return dataloader
        
    except Exception as e:
        print(f"Could not create calibration data loader: {e}")
        print("Falling back to dummy data for calibration...")
        
        # Create dummy data if real data is not available
        dummy_data = []
        for i in range(num_samples):
            # Create dummy chess position data
            boards = torch.randn(18, 8, 8)  # Random board representation
            elos_self = torch.randint(0, 10, (1,))
            elos_oppo = torch.randint(0, 10, (1,))
            legal_moves = torch.zeros(4096)  # Assuming 4096 possible moves
            legal_moves[torch.randint(0, 4096, (20,))] = 1  # Random legal moves
            
            dummy_data.append(("dummy_fen", boards, elos_self, elos_oppo, legal_moves))
        
        return torch.utils.data.DataLoader(dummy_data, batch_size=batch_size, shuffle=False)


def apply_dynamic_quantization(model, output_path):
    """
    Apply dynamic quantization to the model.
    
    Args:
        model: The original MAIA2 model
        output_path: Path to save the quantized model
    
    Returns:
        Quantized model
    """
    print("Applying dynamic quantization...")
    
    # Specify which layers to quantize
    # Focus on Linear layers which benefit most from quantization
    quantized_model = torch.quantization.quantize_dynamic(
        model, 
        {torch.nn.Linear, torch.nn.Conv2d}, 
        dtype=torch.qint8
    )
    
    # Save the quantized model
    torch.save({
        'model_state_dict': quantized_model.state_dict(),
        'quantization_method': 'dynamic',
        'model_class': type(model).__name__
    }, output_path)
    
    print(f"Dynamic quantized model saved to: {output_path}")
    return quantized_model


def apply_static_quantization(model, calibration_loader, output_path):
    """
    Apply static quantization to the model.
    
    Args:
        model: The original MAIA2 model
        calibration_loader: DataLoader for calibration data
        output_path: Path to save the quantized model
    
    Returns:
        Quantized model
    """
    print("Applying static quantization...")
    
    # Set the model to evaluation mode
    model.eval()
    
    # Wrap the model for quantization
    wrapped_model = QuantizedMAIA2Model(model)
    
    # Set quantization configuration
    wrapped_model.qconfig = torch.quantization.get_default_qconfig('fbgemm')
    
    # Prepare the model for quantization
    prepared_model = torch.quantization.prepare(wrapped_model)
    
    print("Running calibration...")
    # Run calibration
    with torch.no_grad():
        for i, batch in enumerate(tqdm(calibration_loader, desc="Calibrating")):
            if len(batch) == 5:  # Real data format
                _, boards, elos_self, elos_oppo, _ = batch
            else:  # Dummy data format
                _, boards, elos_self, elos_oppo, _ = batch[0]
                boards = boards.unsqueeze(0)
                elos_self = elos_self.unsqueeze(0)
                elos_oppo = elos_oppo.unsqueeze(0)
            
            try:
                prepared_model(boards, elos_self, elos_oppo)
            except Exception as e:
                print(f"Calibration step {i} failed: {e}")
                continue
            
            # Limit calibration steps to avoid long processing
            if i >= 50:
                break
    
    # Convert to quantized model
    quantized_model = torch.quantization.convert(prepared_model)
    
    # Save the quantized model
    torch.save({
        'model_state_dict': quantized_model.state_dict(),
        'quantization_method': 'static',
        'model_class': type(model).__name__
    }, output_path)
    
    print(f"Static quantized model saved to: {output_path}")
    return quantized_model


def compare_model_sizes(original_model_path, quantized_model_path):
    """Compare the sizes of original and quantized models."""
    if os.path.exists(original_model_path):
        original_size = os.path.getsize(original_model_path) / (1024 * 1024)  # MB
    else:
        # Estimate based on model parameters
        original_size = sum(p.numel() * 4 for p in original_model_path.parameters()) / (1024 * 1024)  # Assuming float32
    
    quantized_size = os.path.getsize(quantized_model_path) / (1024 * 1024)  # MB
    
    print(f"\nModel Size Comparison:")
    print(f"Original model: {original_size:.2f} MB")
    print(f"Quantized model: {quantized_size:.2f} MB")
    print(f"Size reduction: {((original_size - quantized_size) / original_size * 100):.1f}%")


def benchmark_inference_speed(model, num_iterations=100):
    """
    Benchmark inference speed of a model.
    
    Args:
        model: Model to benchmark
        num_iterations: Number of inference iterations
    
    Returns:
        Average inference time per iteration
    """
    print(f"Benchmarking inference speed ({num_iterations} iterations)...")
    
    # Prepare dummy input
    boards = torch.randn(1, 18, 8, 8)
    elos_self = torch.tensor([5])  # Middle ELO category
    elos_oppo = torch.tensor([5])
    
    model.eval()
    
    # Warmup
    with torch.no_grad():
        for _ in range(10):
            try:
                _ = model(boards, elos_self, elos_oppo)
            except:
                pass
    
    # Benchmark
    start_time = time.time()
    with torch.no_grad():
        for _ in range(num_iterations):
            try:
                _ = model(boards, elos_self, elos_oppo)
            except:
                pass
    
    end_time = time.time()
    avg_time = (end_time - start_time) / num_iterations * 1000  # Convert to milliseconds
    
    print(f"Average inference time: {avg_time:.2f} ms")
    return avg_time


def validate_quantized_model(original_model, quantized_model, num_samples=50):
    """
    Validate that the quantized model produces reasonable outputs.
    
    Args:
        original_model: Original model
        quantized_model: Quantized model  
        num_samples: Number of test samples
    
    Returns:
        Dictionary with validation metrics
    """
    print(f"Validating quantized model with {num_samples} samples...")
    
    # Prepare test inputs
    boards = torch.randn(num_samples, 18, 8, 8)
    elos_self = torch.randint(0, 10, (num_samples,))
    elos_oppo = torch.randint(0, 10, (num_samples,))
    
    original_model.eval()
    quantized_model.eval()
    
    original_outputs = []
    quantized_outputs = []
    
    with torch.no_grad():
        for i in range(num_samples):
            try:
                # Original model output
                orig_out = original_model(boards[i:i+1], elos_self[i:i+1], elos_oppo[i:i+1])
                original_outputs.append(orig_out)
                
                # Quantized model output
                if hasattr(quantized_model, 'model'):
                    quant_out = quantized_model.model(boards[i:i+1], elos_self[i:i+1], elos_oppo[i:i+1])
                else:
                    quant_out = quantized_model(boards[i:i+1], elos_self[i:i+1], elos_oppo[i:i+1])
                quantized_outputs.append(quant_out)
                
            except Exception as e:
                print(f"Validation failed at sample {i}: {e}")
                continue
    
    if not original_outputs or not quantized_outputs:
        print("Validation failed - no successful forward passes")
        return {"validation_successful": False}
    
    # Compare outputs (simplified validation)
    print(f"Validation completed successfully with {len(original_outputs)} samples")
    return {"validation_successful": True, "samples_tested": len(original_outputs)}


def main():
    parser = argparse.ArgumentParser(description='Quantize MAIA2 chess model to 8-bit precision')
    parser.add_argument('--model_type', type=str, choices=['rapid', 'blitz'], default='rapid',
                        help='Type of model to quantize (default: rapid)')
    parser.add_argument('--quantization_method', type=str, choices=['dynamic', 'static'], default='dynamic',
                        help='Quantization method (default: dynamic)')
    parser.add_argument('--output_dir', type=str, default='./quantized_models',
                        help='Output directory for quantized models (default: ./quantized_models)')
    parser.add_argument('--calibration_samples', type=int, default=100,
                        help='Number of samples for calibration (static quantization only, default: 100)')
    parser.add_argument('--benchmark', action='store_true',
                        help='Benchmark inference speed comparison')
    parser.add_argument('--validate', action='store_true',
                        help='Validate quantized model outputs')
    parser.add_argument('--device', type=str, choices=['cpu', 'gpu'], default='cpu',
                        help='Device to run quantization on (default: cpu)')
    
    args = parser.parse_args()
    
    # Create output directory
    os.makedirs(args.output_dir, exist_ok=True)
    
    print("MAIA2 Model 8-bit Quantization")
    print("=" * 50)
    print(f"Model type: {args.model_type}")
    print(f"Quantization method: {args.quantization_method}")
    print(f"Output directory: {args.output_dir}")
    print(f"Device: {args.device}")
    
    try:
        # Load the original model
        print("\nLoading original model...")
        model = from_pretrained(args.model_type, args.device)
        print("Original model loaded successfully!")
        
        # Move to CPU for quantization (quantization typically works on CPU)
        if args.device == 'gpu':
            print("Moving model to CPU for quantization...")
            model = model.cpu()
        
        # Define output path
        output_filename = f"maia2_{args.model_type}_{args.quantization_method}_quantized.pt"
        output_path = os.path.join(args.output_dir, output_filename)
        
        # Apply quantization
        if args.quantization_method == 'dynamic':
            quantized_model = apply_dynamic_quantization(model, output_path)
        else:  # static
            calibration_loader = calibration_data_loader(
                args.model_type, 
                args.calibration_samples, 
                batch_size=8
            )
            quantized_model = apply_static_quantization(model, calibration_loader, output_path)
        
        # Compare model sizes
        try:
            compare_model_sizes(model, output_path)
        except Exception as e:
            print(f"Could not compare model sizes: {e}")
        
        # Benchmark inference speed
        if args.benchmark:
            print("\n" + "="*50)
            print("INFERENCE SPEED BENCHMARK")
            print("="*50)
            
            print("\nOriginal model:")
            original_time = benchmark_inference_speed(model)
            
            print("\nQuantized model:")
            quantized_time = benchmark_inference_speed(quantized_model)
            
            speedup = original_time / quantized_time if quantized_time > 0 else 1.0
            print(f"\nSpeedup: {speedup:.2f}x")
        
        # Validate quantized model
        if args.validate:
            print("\n" + "="*50)
            print("MODEL VALIDATION")
            print("="*50)
            validation_results = validate_quantized_model(model, quantized_model)
            if validation_results["validation_successful"]:
                print("✅ Quantized model validation passed!")
            else:
                print("❌ Quantized model validation failed!")
        
        print(f"\n🎉 Quantization completed successfully!")
        print(f"📁 Quantized model saved to: {output_path}")
        
        # Usage instructions
        print("\n" + "="*50)
        print("USAGE INSTRUCTIONS")
        print("="*50)
        print("To load the quantized model:")
        print(f"""
import torch
checkpoint = torch.load('{output_path}')
# Note: You'll need to recreate the model architecture and load the state dict
        """)
        
    except Exception as e:
        print(f"\n❌ Quantization failed: {e}")
        print("\nMake sure you have:")
        print("1. All MAIA2 modules properly imported")
        print("2. Required dependencies installed (torch, pandas, etc.)")
        print("3. Sufficient memory available")
        return 1
    
    return 0


if __name__ == "__main__":
    exit(main())
#!/usr/bin/env python3
"""
Test script to verify ONNX model inference works correctly.

This script:
1. Loads the ONNX model
2. Runs inference on sample data
3. Compares results with original model (if available)
"""

import numpy as np
import pandas as pd
import chess
import torch
import os
import sys
from typing import Tuple, List

try:
    import onnxruntime as ort
except ImportError:
    print("Error: onnxruntime not installed. Please install it with: pip install onnxruntime")
    sys.exit(1)

# Import local modules
from utils import board_to_tensor, map_to_category, create_elo_dict, get_all_possible_moves

class ONNXInferenceEngine:
    """ONNX inference engine for MAIA2 model."""
    
    def __init__(self, onnx_model_path: str):
        """Initialize the ONNX inference engine."""
        if not os.path.exists(onnx_model_path):
            raise FileNotFoundError(f"ONNX model not found at {onnx_model_path}")
        
        print(f"Loading ONNX model from {onnx_model_path}")
        
        # Create ONNX Runtime session
        self.session = ort.InferenceSession(onnx_model_path)
        
        # Get input and output names
        self.input_names = [input.name for input in self.session.get_inputs()]
        self.output_names = [output.name for output in self.session.get_outputs()]
        
        print(f"Input names: {self.input_names}")
        print(f"Output names: {self.output_names}")
        
        # Initialize move and ELO mappings
        self.all_moves = get_all_possible_moves()
        self.all_moves_dict = {move: i for i, move in enumerate(self.all_moves)}
        self.elo_dict = create_elo_dict()
        
        print(f"Model loaded successfully with {len(self.all_moves)} possible moves")
    
    def predict_move(self, board_fen: str, active_elo: int, opponent_elo: int) -> Tuple[str, float, List[Tuple[str, float]]]:
        """
        Predict the best move for a given position.
        
        Args:
            board_fen: FEN string of the board position
            active_elo: ELO rating of the active player
            opponent_elo: ELO rating of the opponent
            
        Returns:
            Tuple of (best_move, confidence, top_moves_list)
        """
        # Parse board
        board = chess.Board(board_fen)
        
        # Convert to tensor
        board_tensor = board_to_tensor(board).unsqueeze(0).numpy()  # Add batch dimension
        
        # Map ELO ratings
        elo_self = np.array([map_to_category(active_elo, self.elo_dict)], dtype=np.int64)
        elo_oppo = np.array([map_to_category(opponent_elo, self.elo_dict)], dtype=np.int64)
        
        # Prepare inputs
        inputs = {
            self.input_names[0]: board_tensor.astype(np.float32),
            self.input_names[1]: elo_self,
            self.input_names[2]: elo_oppo
        }
        
        # Run inference
        outputs = self.session.run(self.output_names, inputs)
        move_logits = outputs[0][0]  # Remove batch dimension
        
        # Get legal moves
        legal_moves = [move.uci() for move in board.legal_moves]
        legal_move_indices = [self.all_moves_dict[move] for move in legal_moves if move in self.all_moves_dict]
        
        # Filter logits to only legal moves
        legal_logits = [(i, move_logits[i]) for i in legal_move_indices]
        legal_logits.sort(key=lambda x: x[1], reverse=True)
        
        # Convert to move strings with probabilities
        top_moves = []
        for idx, logit in legal_logits[:10]:  # Top 10 moves
            move = self.all_moves[idx]
            probability = float(np.exp(logit) / np.sum(np.exp([l[1] for l in legal_logits])))
            top_moves.append((move, probability))
        
        best_move = top_moves[0][0] if top_moves else "No legal moves"
        confidence = top_moves[0][1] if top_moves else 0.0
        
        return best_move, confidence, top_moves

def test_onnx_model(onnx_model_path: str, data_path: str, num_samples: int = 10):
    """Test the ONNX model on sample data."""
    print("=== Testing ONNX Model ===")
    
    # Initialize inference engine
    try:
        engine = ONNXInferenceEngine(onnx_model_path)
    except Exception as e:
        print(f"Error loading ONNX model: {e}")
        return
    
    # Load test data
    if not os.path.exists(data_path):
        print(f"Error: Test data not found at {data_path}")
        return
    
    print(f"Loading test data from {data_path}")
    data = pd.read_csv(data_path)
    
    # Sample random positions
    test_samples = data.sample(n=min(num_samples, len(data)), random_state=42)
    
    print(f"\nTesting on {len(test_samples)} positions...")
    print("-" * 80)
    
    for i, (_, row) in enumerate(test_samples.iterrows()):
        print(f"\nTest {i+1}/{len(test_samples)}:")
        print(f"Board: {row['board']}")
        print(f"Active ELO: {row['active_elo']}, Opponent ELO: {row['opponent_elo']}")
        print(f"Actual move: {row['move']}")
        
        try:
            # Predict move
            best_move, confidence, top_moves = engine.predict_move(
                row['board'], 
                row['active_elo'], 
                row['opponent_elo']
            )
            
            print(f"Predicted move: {best_move} (confidence: {confidence:.4f})")
            
            # Check if actual move is in top predictions
            actual_move = row['move']
            actual_in_top = any(move == actual_move for move, _ in top_moves)
            actual_rank = next((i+1 for i, (move, _) in enumerate(top_moves) if move == actual_move), "Not found")
            
            print(f"Actual move rank: {actual_rank}")
            
            # Show top 5 moves
            print("Top 5 predicted moves:")
            for j, (move, prob) in enumerate(top_moves[:5]):
                marker = " ✓" if move == actual_move else ""
                print(f"  {j+1}. {move}: {prob:.4f}{marker}")
                
        except Exception as e:
            print(f"Error during prediction: {e}")
            import traceback
            traceback.print_exc()
        
        print("-" * 40)

def benchmark_inference_speed(onnx_model_path: str, num_iterations: int = 100):
    """Benchmark inference speed of the ONNX model."""
    print(f"\n=== Benchmarking Inference Speed ({num_iterations} iterations) ===")
    
    try:
        engine = ONNXInferenceEngine(onnx_model_path)
    except Exception as e:
        print(f"Error loading ONNX model: {e}")
        return
    
    # Create dummy data
    dummy_fen = "rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1"
    dummy_elo = 1500
    
    import time
    
    # Warm up
    for _ in range(10):
        engine.predict_move(dummy_fen, dummy_elo, dummy_elo)
    
    # Benchmark
    start_time = time.time()
    for _ in range(num_iterations):
        engine.predict_move(dummy_fen, dummy_elo, dummy_elo)
    end_time = time.time()
    
    total_time = end_time - start_time
    avg_time = total_time / num_iterations
    
    print(f"Total time: {total_time:.4f} seconds")
    print(f"Average time per inference: {avg_time:.4f} seconds")
    print(f"Inferences per second: {1/avg_time:.2f}")

def main():
    """Main function to test the ONNX model."""
    # Configuration
    ONNX_MODEL_PATH = "C:/Users/<USER>/Desktop/apps/trainNNIUE/Q3/maia2/maia2_inference.onnx"
    DATA_PATH = "C:/Users/<USER>/Desktop/apps/trainNNIUE/Q3/maia2/rapid_sample_data.csv"
    
    print("=== MAIA2 ONNX Model Testing ===")
    print(f"ONNX model path: {ONNX_MODEL_PATH}")
    print(f"Test data path: {DATA_PATH}")
    
    # Check if files exist
    if not os.path.exists(ONNX_MODEL_PATH):
        print(f"Error: ONNX model not found at {ONNX_MODEL_PATH}")
        print("Please run quantize_and_convert_to_onnx.py first to create the ONNX model.")
        return
    
    if not os.path.exists(DATA_PATH):
        print(f"Error: Test data not found at {DATA_PATH}")
        return
    
    try:
        # Test model accuracy
        test_onnx_model(ONNX_MODEL_PATH, DATA_PATH, num_samples=5)
        
        # Benchmark speed
        benchmark_inference_speed(ONNX_MODEL_PATH, num_iterations=50)
        
        print("\n=== Testing Complete ===")
        
    except Exception as e:
        print(f"Error during testing: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()

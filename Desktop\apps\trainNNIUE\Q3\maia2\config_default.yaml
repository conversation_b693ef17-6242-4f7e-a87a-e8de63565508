# Default configuration for MAIA2 model
# This file provides default settings if config.yaml is not available

# Model architecture parameters
input_channels: 18          # Number of input channels for board representation
dim_cnn: 256               # CNN dimension
num_blocks_cnn: 8          # Number of CNN blocks
vit_length: 256            # Vision Transformer sequence length
dim_vit: 512               # Vision Transformer dimension
num_blocks_vit: 8          # Number of Vision Transformer blocks
elo_dim: 64                # ELO embedding dimension

# Training parameters (not used for inference but needed for model initialization)
batch_size: 32
num_workers: 4
learning_rate: 0.001

# Loss function weights
side_info: true            # Whether to use side information loss
value: true                # Whether to use value loss
side_info_coefficient: 0.1 # Weight for side information loss
value_coefficient: 0.1     # Weight for value loss

# Data processing parameters
first_n_moves: 10          # Skip first N moves in training data
max_ply: 200               # Maximum ply to consider
clock_threshold: 30        # Minimum clock time threshold
max_games_per_elo_range: 1000  # Maximum games per ELO range

# Quantization parameters
quantization_backend: "fbgemm"  # Quantization backend (fbgemm for CPU, qnnpack for mobile)
calibration_samples: 1000       # Number of samples for quantization calibration
calibration_batch_size: 32      # Batch size for calibration

# ONNX export parameters
onnx_opset_version: 11     # ONNX opset version
onnx_dynamic_axes: true    # Enable dynamic batch size in ONNX

# Verbose output
verbose: false

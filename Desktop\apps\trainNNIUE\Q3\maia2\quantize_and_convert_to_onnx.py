#!/usr/bin/env python3
"""
Script to quantize MAIA2 model and convert to ONNX inference format.

This script:
1. Loads the MAIA2 model from Rapidweights.v2.pt
2. Applies post-training quantization
3. Converts the quantized model to ONNX format for inference
4. Uses rapid_sample_data.csv for calibration data during quantization
"""

import torch
import torch.nn as nn
import torch.quantization as quantization
import pandas as pd
import numpy as np
import chess
import os
import sys
from typing import Tuple, Dict, Any
import warnings
warnings.filterwarnings("ignore")

# Import local modules
from main import MAIA2Model
from utils import (
    get_all_possible_moves, 
    create_elo_dict, 
    parse_args, 
    board_to_tensor,
    map_to_category
)

class QuantizedMAIA2Model(nn.Module):
    """Wrapper for quantized MAIA2 model to handle inference."""
    
    def __init__(self, original_model: MAIA2Model):
        super().__init__()
        self.model = original_model
        self.quant = torch.quantization.QuantStub()
        self.dequant = torch.quantization.DeQuantStub()
    
    def forward(self, boards: torch.Tensor, elos_self: torch.Tensor, elos_oppo: torch.Tensor):
        boards = self.quant(boards)
        elos_self = self.quant(elos_self)
        elos_oppo = self.quant(elos_oppo)
        
        logits_maia, logits_side_info, logits_value = self.model(boards, elos_self, elos_oppo)
        
        logits_maia = self.dequant(logits_maia)
        logits_side_info = self.dequant(logits_side_info)
        logits_value = self.dequant(logits_value)
        
        return logits_maia, logits_side_info, logits_value

class CalibrationDataset:
    """Dataset for model calibration during quantization."""
    
    def __init__(self, csv_path: str, all_moves_dict: Dict, elo_dict: Dict, max_samples: int = 1000):
        self.data = pd.read_csv(csv_path)
        self.all_moves_dict = all_moves_dict
        self.elo_dict = elo_dict
        
        # Sample data for calibration
        if len(self.data) > max_samples:
            self.data = self.data.sample(n=max_samples, random_state=42)
        
        print(f"Using {len(self.data)} samples for calibration")
    
    def __len__(self):
        return len(self.data)
    
    def __getitem__(self, idx):
        row = self.data.iloc[idx]
        
        # Parse board position
        board = chess.Board(row['board'])
        board_tensor = board_to_tensor(board)
        
        # Map ELO ratings to categories
        elo_self = map_to_category(row['active_elo'], self.elo_dict)
        elo_oppo = map_to_category(row['opponent_elo'], self.elo_dict)
        
        return board_tensor, torch.tensor(elo_self), torch.tensor(elo_oppo)

def load_model(model_path: str, device: str = 'cpu') -> MAIA2Model:
    """Load the MAIA2 model from checkpoint."""
    print(f"Loading model from {model_path}")
    
    # Get model configuration
    try:
        cfg = parse_args("config.yaml")
        print("Using config.yaml")
    except FileNotFoundError:
        try:
            cfg = parse_args("config_default.yaml")
            print("Using config_default.yaml")
        except FileNotFoundError:
            # Create a basic config if neither config file exists
            print("Warning: No config files found, using hardcoded default configuration")
            class DefaultConfig:
                input_channels = 18
                dim_cnn = 256
                num_blocks_cnn = 8
                vit_length = 256
                dim_vit = 512
                num_blocks_vit = 8
                elo_dim = 64
                side_info = True
                value = True
            cfg = DefaultConfig()
    
    # Initialize model
    all_moves = get_all_possible_moves()
    elo_dict = create_elo_dict()
    
    model = MAIA2Model(len(all_moves), elo_dict, cfg)
    
    # Load checkpoint
    checkpoint = torch.load(model_path, map_location=device)
    
    # Handle DataParallel wrapper
    state_dict = checkpoint['model_state_dict']
    if any(key.startswith('module.') for key in state_dict.keys()):
        # Remove 'module.' prefix from DataParallel
        new_state_dict = {}
        for key, value in state_dict.items():
            new_key = key.replace('module.', '')
            new_state_dict[new_key] = value
        state_dict = new_state_dict
    
    model.load_state_dict(state_dict)
    model.eval()
    
    print("Model loaded successfully")
    return model, all_moves, elo_dict, cfg

def calibrate_model(model: QuantizedMAIA2Model, calibration_data: CalibrationDataset, 
                   batch_size: int = 32) -> None:
    """Calibrate the quantized model using sample data."""
    print("Calibrating model for quantization...")
    
    model.eval()
    with torch.no_grad():
        for i in range(0, min(len(calibration_data), 100), batch_size):
            batch_boards = []
            batch_elos_self = []
            batch_elos_oppo = []
            
            for j in range(i, min(i + batch_size, len(calibration_data))):
                board_tensor, elo_self, elo_oppo = calibration_data[j]
                batch_boards.append(board_tensor)
                batch_elos_self.append(elo_self)
                batch_elos_oppo.append(elo_oppo)
            
            if batch_boards:
                boards = torch.stack(batch_boards)
                elos_self = torch.stack(batch_elos_self)
                elos_oppo = torch.stack(batch_elos_oppo)
                
                # Forward pass for calibration
                _ = model(boards, elos_self, elos_oppo)
    
    print("Calibration completed")

def quantize_model(model: MAIA2Model, calibration_data: CalibrationDataset) -> QuantizedMAIA2Model:
    """Apply post-training quantization to the model."""
    print("Starting model quantization...")
    
    # Wrap model for quantization
    quantized_model = QuantizedMAIA2Model(model)
    
    # Set quantization configuration
    quantized_model.qconfig = torch.quantization.get_default_qconfig('fbgemm')
    
    # Prepare model for quantization
    torch.quantization.prepare(quantized_model, inplace=True)
    
    # Calibrate the model
    calibrate_model(quantized_model, calibration_data)
    
    # Convert to quantized model
    torch.quantization.convert(quantized_model, inplace=True)
    
    print("Model quantization completed")
    return quantized_model

def convert_to_onnx(model: nn.Module, output_path: str, all_moves: list, elo_dict: dict) -> None:
    """Convert the quantized model to ONNX format."""
    print(f"Converting model to ONNX format: {output_path}")
    
    # Create dummy inputs for ONNX export
    batch_size = 1
    board_input = torch.randn(batch_size, 18, 8, 8)  # 18 channels for board representation
    elo_self_input = torch.randint(0, len(elo_dict), (batch_size,))
    elo_oppo_input = torch.randint(0, len(elo_dict), (batch_size,))
    
    # Input names for ONNX
    input_names = ['board', 'elo_self', 'elo_oppo']
    output_names = ['move_logits', 'side_info_logits', 'value_logits']
    
    # Dynamic axes for variable batch size
    dynamic_axes = {
        'board': {0: 'batch_size'},
        'elo_self': {0: 'batch_size'},
        'elo_oppo': {0: 'batch_size'},
        'move_logits': {0: 'batch_size'},
        'side_info_logits': {0: 'batch_size'},
        'value_logits': {0: 'batch_size'}
    }
    
    model.eval()
    with torch.no_grad():
        torch.onnx.export(
            model,
            (board_input, elo_self_input, elo_oppo_input),
            output_path,
            export_params=True,
            opset_version=11,
            do_constant_folding=True,
            input_names=input_names,
            output_names=output_names,
            dynamic_axes=dynamic_axes,
            verbose=False
        )
    
    print(f"ONNX model saved to {output_path}")

def main():
    """Main function to orchestrate the quantization and conversion process."""
    # Configuration
    MODEL_PATH = "C:/Users/<USER>/Desktop/apps/trainNNIUE/Q3/maia2/Rapidweights.v2.pt"
    DATA_PATH = "C:/Users/<USER>/Desktop/apps/trainNNIUE/Q3/maia2/rapid_sample_data.csv"
    OUTPUT_DIR = "C:/Users/<USER>/Desktop/apps/trainNNIUE/Q3/maia2"
    
    QUANTIZED_MODEL_PATH = os.path.join(OUTPUT_DIR, "maia2_quantized.pt")
    ONNX_MODEL_PATH = os.path.join(OUTPUT_DIR, "maia2_inference.onnx")
    
    print("=== MAIA2 Model Quantization and ONNX Conversion ===")
    print(f"Model path: {MODEL_PATH}")
    print(f"Data path: {DATA_PATH}")
    print(f"Output directory: {OUTPUT_DIR}")
    
    # Check if input files exist
    if not os.path.exists(MODEL_PATH):
        print(f"Error: Model file not found at {MODEL_PATH}")
        return
    
    if not os.path.exists(DATA_PATH):
        print(f"Error: Data file not found at {DATA_PATH}")
        return
    
    try:
        # Load the original model
        model, all_moves, elo_dict, cfg = load_model(MODEL_PATH)
        
        # Create calibration dataset
        all_moves_dict = {move: i for i, move in enumerate(all_moves)}
        calibration_data = CalibrationDataset(DATA_PATH, all_moves_dict, elo_dict)
        
        # Quantize the model
        quantized_model = quantize_model(model, calibration_data)
        
        # Save quantized model
        torch.save(quantized_model.state_dict(), QUANTIZED_MODEL_PATH)
        print(f"Quantized model saved to {QUANTIZED_MODEL_PATH}")
        
        # Convert to ONNX
        convert_to_onnx(quantized_model, ONNX_MODEL_PATH, all_moves, elo_dict)
        
        # Print model information
        print("\n=== Model Information ===")
        print(f"Number of possible moves: {len(all_moves)}")
        print(f"ELO categories: {len(elo_dict)}")
        print(f"Model configuration: {cfg.__dict__ if hasattr(cfg, '__dict__') else 'Default config used'}")
        
        print("\n=== Conversion Complete ===")
        print(f"Quantized PyTorch model: {QUANTIZED_MODEL_PATH}")
        print(f"ONNX inference model: {ONNX_MODEL_PATH}")
        
    except Exception as e:
        print(f"Error during conversion: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()

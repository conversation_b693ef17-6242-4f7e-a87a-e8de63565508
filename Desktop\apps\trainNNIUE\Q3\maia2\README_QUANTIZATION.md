# MAIA2 Model Quantization and ONNX Conversion

This directory contains scripts to quantize the MAIA2 chess model and convert it to ONNX format for efficient inference.

## Files

- `quantize_and_convert_to_onnx.py` - Main script for quantization and ONNX conversion
- `test_onnx_inference.py` - Test script to verify ONNX model functionality
- `requirements.txt` - Updated dependencies including ONNX support

## Prerequisites

1. **Install Dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

2. **Required Files**:
   - `Rapidweights.v2.pt` - Pre-trained MAIA2 model weights
   - `rapid_sample_data.csv` - Sample data for calibration during quantization
   - `config.yaml` - Model configuration (optional, defaults will be used if not found)

## Usage

### Step 1: Quantize and Convert Model

Run the main quantization script:

```bash
python quantize_and_convert_to_onnx.py
```

This script will:
1. Load the MAIA2 model from `Rapidweights.v2.pt`
2. Apply post-training quantization using sample data for calibration
3. Save the quantized PyTorch model as `maia2_quantized.pt`
4. Convert the quantized model to ONNX format as `maia2_inference.onnx`

### Step 2: Test ONNX Model

Verify the ONNX model works correctly:

```bash
python test_onnx_inference.py
```

This script will:
1. Load the ONNX model
2. Test inference on sample positions from the CSV data
3. Compare predictions with actual moves
4. Benchmark inference speed

## Output Files

After successful execution, you will have:

- `maia2_quantized.pt` - Quantized PyTorch model (smaller size, faster inference)
- `maia2_inference.onnx` - ONNX model for cross-platform inference

## Model Architecture

The MAIA2 model consists of:
- **Input**: Chess board representation (18 channels, 8x8), player ELO ratings
- **Architecture**: CNN backbone + Vision Transformer with ELO-aware attention
- **Output**: 
  - Move logits (probability distribution over all possible moves)
  - Side information (piece types, captures, checks, etc.)
  - Position value estimation

## Quantization Details

The quantization process:
1. **Method**: Post-training quantization using PyTorch's built-in quantization
2. **Calibration**: Uses sample data from `rapid_sample_data.csv`
3. **Backend**: FBGEMM (optimized for CPU inference)
4. **Precision**: INT8 weights and activations where possible

## ONNX Model Usage

The ONNX model can be used with:
- **ONNX Runtime** (Python, C++, C#, Java)
- **TensorRT** (NVIDIA GPUs)
- **OpenVINO** (Intel hardware)
- **CoreML** (Apple devices)

### Example ONNX Inference (Python)

```python
import onnxruntime as ort
import numpy as np

# Load model
session = ort.InferenceSession("maia2_inference.onnx")

# Prepare inputs
board = np.random.randn(1, 18, 8, 8).astype(np.float32)  # Board representation
elo_self = np.array([5], dtype=np.int64)  # ELO category
elo_oppo = np.array([5], dtype=np.int64)  # Opponent ELO category

# Run inference
outputs = session.run(None, {
    "board": board,
    "elo_self": elo_self,
    "elo_oppo": elo_oppo
})

move_logits, side_info_logits, value_logits = outputs
```

## Performance Benefits

Quantization typically provides:
- **Model Size**: 50-75% reduction
- **Inference Speed**: 2-4x faster on CPU
- **Memory Usage**: Significantly reduced
- **Accuracy**: Minimal loss (typically <1% degradation)

## Troubleshooting

### Common Issues

1. **Missing Dependencies**:
   ```bash
   pip install torch onnx onnxruntime
   ```

2. **CUDA/GPU Issues**:
   - The script is designed for CPU inference
   - For GPU quantization, modify the device settings in the script

3. **Memory Issues**:
   - Reduce calibration batch size in `quantize_and_convert_to_onnx.py`
   - Use fewer calibration samples

4. **Config File Missing**:
   - The script will use default configuration if `config.yaml` is not found
   - Ensure model architecture matches the checkpoint

### Validation

To verify the quantized model maintains accuracy:
1. Run `test_onnx_inference.py`
2. Check that predicted moves are reasonable
3. Compare inference speed with original model

## Advanced Usage

### Custom Quantization

To modify quantization settings, edit the `quantize_model` function:

```python
# Different quantization backend
quantized_model.qconfig = torch.quantization.get_default_qconfig('qnnpack')

# Custom quantization configuration
quantized_model.qconfig = torch.quantization.QConfig(
    activation=torch.quantization.MinMaxObserver.with_args(dtype=torch.quint8),
    weight=torch.quantization.MinMaxObserver.with_args(dtype=torch.qint8)
)
```

### Deployment

For production deployment:
1. Use the ONNX model with ONNX Runtime
2. Consider TensorRT for NVIDIA GPUs
3. Use OpenVINO for Intel hardware optimization
4. Implement proper error handling and input validation

## Support

For issues or questions:
1. Check the console output for detailed error messages
2. Verify all input files exist and are accessible
3. Ensure dependencies are correctly installed
4. Check PyTorch and ONNX version compatibility

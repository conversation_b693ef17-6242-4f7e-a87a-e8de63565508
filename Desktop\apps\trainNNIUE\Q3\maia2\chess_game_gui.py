#!/usr/bin/env python3
"""
Complete Chess Game with MAIA2 Quantized Model

This script creates a fully functional chess game where you can play against
the MAIA2 model at different ELO levels using the quantized ONNX model.

Features:
- Interactive chess board GUI
- Play as White or Black
- Adjustable MAIA ELO level (800-2800)
- Move validation and legal move highlighting
- Game state tracking (check, checkmate, stalemate)
- Move history and notation
- Save/Load games in PGN format
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import chess
import chess.pgn
import chess.svg
import numpy as np
import threading
import time
import os
import sys
from typing import Optional, List, Tuple
import warnings
warnings.filterwarnings("ignore")

try:
    import onnxruntime as ort
except ImportError:
    print("Error: onnxruntime not installed. Please install: pip install onnxruntime")
    sys.exit(1)

# Import local modules
try:
    from utils import board_to_tensor, map_to_category, create_elo_dict, get_all_possible_moves
except ImportError:
    import sys
    import os
    sys.path.append(os.path.dirname(os.path.abspath(__file__)))
    from utils import board_to_tensor, map_to_category, create_elo_dict, get_all_possible_moves

class MAIA2Engine:
    """MAIA2 chess engine using quantized ONNX model."""
    
    def __init__(self, model_path: str):
        """Initialize the MAIA2 engine."""
        if not os.path.exists(model_path):
            raise FileNotFoundError(f"Model not found: {model_path}")
        
        print(f"Loading MAIA2 model: {model_path}")
        self.session = ort.InferenceSession(model_path)
        self.all_moves = get_all_possible_moves()
        self.all_moves_dict = {move: i for i, move in enumerate(self.all_moves)}
        self.elo_dict = create_elo_dict()
        
        print(f"MAIA2 engine loaded with {len(self.all_moves)} possible moves")
    
    def get_best_move(self, board: chess.Board, player_elo: int, opponent_elo: int, 
                     top_k: int = 5) -> List[Tuple[str, float]]:
        """Get the best moves for the current position."""
        # Convert board to tensor
        board_tensor = board_to_tensor(board).unsqueeze(0).numpy().astype(np.float32)
        
        # Map ELO ratings to categories
        elo_self = np.array([map_to_category(player_elo, self.elo_dict)], dtype=np.int64)
        elo_oppo = np.array([map_to_category(opponent_elo, self.elo_dict)], dtype=np.int64)
        
        # Prepare inputs
        inputs = {
            'board': board_tensor,
            'elo_self': elo_self,
            'elo_oppo': elo_oppo
        }
        
        # Run inference
        outputs = self.session.run(None, inputs)
        move_logits = outputs[0][0]  # Remove batch dimension
        
        # Get legal moves
        legal_moves = [move.uci() for move in board.legal_moves]
        legal_move_indices = [self.all_moves_dict[move] for move in legal_moves 
                             if move in self.all_moves_dict]
        
        if not legal_move_indices:
            return []
        
        # Filter logits to only legal moves and get probabilities
        legal_logits = [(i, move_logits[i]) for i in legal_move_indices]
        legal_logits.sort(key=lambda x: x[1], reverse=True)
        
        # Convert to probabilities
        logit_values = [logit for _, logit in legal_logits]
        exp_logits = np.exp(logit_values)
        probabilities = exp_logits / np.sum(exp_logits)
        
        # Get top moves with probabilities
        top_moves = []
        for i in range(min(top_k, len(legal_logits))):
            move_idx, _ = legal_logits[i]
            move = self.all_moves[move_idx]
            probability = probabilities[i]
            top_moves.append((move, probability))
        
        return top_moves

class ChessBoard(tk.Canvas):
    """Interactive chess board widget."""
    
    def __init__(self, parent, size=480):
        super().__init__(parent, width=size, height=size, bg='white')
        self.size = size
        self.square_size = size // 8
        self.board = chess.Board()
        self.selected_square = None
        self.legal_moves = []
        self.flipped = False
        self.move_callback = None
        
        # Colors
        self.light_color = '#F0D9B5'
        self.dark_color = '#B58863'
        self.highlight_color = '#FFFF00'
        self.legal_move_color = '#00FF00'
        
        # Piece symbols (Unicode)
        self.piece_symbols = {
            'P': '♙', 'R': '♖', 'N': '♘', 'B': '♗', 'Q': '♕', 'K': '♔',
            'p': '♟', 'r': '♜', 'n': '♞', 'b': '♝', 'q': '♛', 'k': '♚'
        }
        
        self.bind('<Button-1>', self.on_click)
        self.draw_board()
    
    def set_move_callback(self, callback):
        """Set callback function for when a move is made."""
        self.move_callback = callback
    
    def flip_board(self):
        """Flip the board perspective."""
        self.flipped = not self.flipped
        self.draw_board()
    
    def square_to_coords(self, square):
        """Convert chess square to canvas coordinates."""
        file = chess.square_file(square)
        rank = chess.square_rank(square)
        
        if self.flipped:
            x = (7 - file) * self.square_size
            y = rank * self.square_size
        else:
            x = file * self.square_size
            y = (7 - rank) * self.square_size
        
        return x, y
    
    def coords_to_square(self, x, y):
        """Convert canvas coordinates to chess square."""
        file = x // self.square_size
        rank = 7 - (y // self.square_size)
        
        if self.flipped:
            file = 7 - file
            rank = 7 - rank
        
        if 0 <= file <= 7 and 0 <= rank <= 7:
            return chess.square(file, rank)
        return None
    
    def draw_board(self):
        """Draw the chess board and pieces."""
        self.delete('all')
        
        # Draw squares
        for rank in range(8):
            for file in range(8):
                square = chess.square(file, rank)
                x, y = self.square_to_coords(square)
                
                # Square color
                color = self.light_color if (file + rank) % 2 == 0 else self.dark_color
                
                # Highlight selected square
                if square == self.selected_square:
                    color = self.highlight_color
                
                # Highlight legal move squares
                if square in [move.to_square for move in self.legal_moves]:
                    color = self.legal_move_color
                
                self.create_rectangle(x, y, x + self.square_size, y + self.square_size,
                                    fill=color, outline='black')
                
                # Draw piece
                piece = self.board.piece_at(square)
                if piece:
                    symbol = self.piece_symbols.get(piece.symbol(), piece.symbol())
                    self.create_text(x + self.square_size//2, y + self.square_size//2,
                                   text=symbol, font=('Arial', self.square_size//2),
                                   fill='black')
        
        # Draw file and rank labels
        for i in range(8):
            # Files (a-h)
            file_label = chr(ord('a') + (i if not self.flipped else 7-i))
            self.create_text(i * self.square_size + self.square_size//2, 
                           self.size - 10, text=file_label, font=('Arial', 10))
            
            # Ranks (1-8)
            rank_label = str((8-i) if not self.flipped else (i+1))
            self.create_text(10, i * self.square_size + self.square_size//2, 
                           text=rank_label, font=('Arial', 10))
    
    def on_click(self, event):
        """Handle mouse clicks on the board."""
        square = self.coords_to_square(event.x, event.y)
        if square is None:
            return
        
        if self.selected_square is None:
            # Select a square with a piece
            piece = self.board.piece_at(square)
            if piece and piece.color == self.board.turn:
                self.selected_square = square
                self.legal_moves = [move for move in self.board.legal_moves 
                                  if move.from_square == square]
                self.draw_board()
        else:
            # Try to make a move
            move = None
            for legal_move in self.board.legal_moves:
                if (legal_move.from_square == self.selected_square and 
                    legal_move.to_square == square):
                    move = legal_move
                    break
            
            if move:
                if self.move_callback:
                    self.move_callback(move)
            
            # Deselect
            self.selected_square = None
            self.legal_moves = []
            self.draw_board()
    
    def make_move(self, move):
        """Make a move on the board."""
        if move in self.board.legal_moves:
            self.board.push(move)
            self.selected_square = None
            self.legal_moves = []
            self.draw_board()
            return True
        return False
    
    def set_position(self, board):
        """Set the board position."""
        self.board = board.copy()
        self.selected_square = None
        self.legal_moves = []
        self.draw_board()

class ChessGameGUI:
    """Main chess game GUI application."""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("MAIA2 Chess Game")
        self.root.geometry("800x600")
        
        # Game state
        self.board = chess.Board()
        self.engine = None
        self.game_history = []
        self.player_color = chess.WHITE
        self.maia_elo = 1500
        self.player_elo = 1500
        self.thinking = False
        
        self.setup_ui()
        self.load_engine()
    
    def setup_ui(self):
        """Setup the user interface."""
        # Main frame
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Left panel - Chess board
        board_frame = ttk.Frame(main_frame)
        board_frame.pack(side=tk.LEFT, padx=(0, 10))
        
        self.chess_board = ChessBoard(board_frame)
        self.chess_board.pack()
        self.chess_board.set_move_callback(self.on_player_move)
        
        # Right panel - Controls and info
        control_frame = ttk.Frame(main_frame)
        control_frame.pack(side=tk.RIGHT, fill=tk.Y)
        
        # Game controls
        controls_group = ttk.LabelFrame(control_frame, text="Game Controls")
        controls_group.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Button(controls_group, text="New Game", command=self.new_game).pack(fill=tk.X, pady=2)
        ttk.Button(controls_group, text="Flip Board", command=self.flip_board).pack(fill=tk.X, pady=2)
        ttk.Button(controls_group, text="Save Game", command=self.save_game).pack(fill=tk.X, pady=2)
        ttk.Button(controls_group, text="Load Game", command=self.load_game).pack(fill=tk.X, pady=2)
        
        # Player settings
        settings_group = ttk.LabelFrame(control_frame, text="Settings")
        settings_group.pack(fill=tk.X, pady=(0, 10))
        
        # Player color
        ttk.Label(settings_group, text="Play as:").pack(anchor=tk.W)
        self.color_var = tk.StringVar(value="White")
        color_frame = ttk.Frame(settings_group)
        color_frame.pack(fill=tk.X)
        ttk.Radiobutton(color_frame, text="White", variable=self.color_var, 
                       value="White", command=self.on_color_change).pack(side=tk.LEFT)
        ttk.Radiobutton(color_frame, text="Black", variable=self.color_var, 
                       value="Black", command=self.on_color_change).pack(side=tk.LEFT)
        
        # MAIA ELO
        ttk.Label(settings_group, text="MAIA ELO:").pack(anchor=tk.W, pady=(10, 0))
        self.maia_elo_var = tk.IntVar(value=1500)
        elo_frame = ttk.Frame(settings_group)
        elo_frame.pack(fill=tk.X)
        self.elo_scale = ttk.Scale(elo_frame, from_=800, to=2800, variable=self.maia_elo_var,
                                  orient=tk.HORIZONTAL, command=self.on_elo_change)
        self.elo_scale.pack(fill=tk.X)
        self.elo_label = ttk.Label(elo_frame, text="1500")
        self.elo_label.pack()
        
        # Player ELO
        ttk.Label(settings_group, text="Your ELO:").pack(anchor=tk.W, pady=(10, 0))
        self.player_elo_var = tk.IntVar(value=1500)
        player_elo_frame = ttk.Frame(settings_group)
        player_elo_frame.pack(fill=tk.X)
        ttk.Scale(player_elo_frame, from_=800, to=2800, variable=self.player_elo_var,
                 orient=tk.HORIZONTAL, command=self.on_player_elo_change).pack(fill=tk.X)
        self.player_elo_label = ttk.Label(player_elo_frame, text="1500")
        self.player_elo_label.pack()
        
        # Game info
        info_group = ttk.LabelFrame(control_frame, text="Game Info")
        info_group.pack(fill=tk.X, pady=(0, 10))
        
        self.status_label = ttk.Label(info_group, text="White to move")
        self.status_label.pack(anchor=tk.W)
        
        self.thinking_label = ttk.Label(info_group, text="", foreground="blue")
        self.thinking_label.pack(anchor=tk.W)
        
        # Move history
        history_group = ttk.LabelFrame(control_frame, text="Move History")
        history_group.pack(fill=tk.BOTH, expand=True)
        
        self.history_text = tk.Text(history_group, height=10, width=25)
        history_scroll = ttk.Scrollbar(history_group, orient=tk.VERTICAL, command=self.history_text.yview)
        self.history_text.configure(yscrollcommand=history_scroll.set)
        self.history_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        history_scroll.pack(side=tk.RIGHT, fill=tk.Y)
    
    def load_engine(self):
        """Load the MAIA2 engine."""
        model_path = "maia2_quantized_static.onnx"
        if not os.path.exists(model_path):
            messagebox.showerror("Error", f"Model file not found: {model_path}")
            return
        
        try:
            self.engine = MAIA2Engine(model_path)
            messagebox.showinfo("Success", "MAIA2 engine loaded successfully!")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to load engine: {e}")
    
    def on_color_change(self):
        """Handle player color change."""
        self.player_color = chess.WHITE if self.color_var.get() == "White" else chess.BLACK
        self.new_game()
    
    def on_elo_change(self, value):
        """Handle MAIA ELO change."""
        self.maia_elo = int(float(value))
        self.elo_label.config(text=str(self.maia_elo))
    
    def on_player_elo_change(self, value):
        """Handle player ELO change."""
        self.player_elo = int(float(value))
        self.player_elo_label.config(text=str(self.player_elo))
    
    def new_game(self):
        """Start a new game."""
        self.board = chess.Board()
        self.game_history = []
        self.chess_board.set_position(self.board)
        self.update_status()
        self.update_history()
        
        # If player is black, make MAIA move first
        if self.player_color == chess.BLACK:
            self.root.after(500, self.make_maia_move)
    
    def flip_board(self):
        """Flip the chess board."""
        self.chess_board.flip_board()
    
    def on_player_move(self, move):
        """Handle player move."""
        if self.thinking or self.board.turn != self.player_color:
            return
        
        if self.chess_board.make_move(move):
            self.game_history.append(move)
            self.update_status()
            self.update_history()
            
            if not self.board.is_game_over():
                self.root.after(500, self.make_maia_move)
    
    def make_maia_move(self):
        """Make MAIA move in a separate thread."""
        if self.thinking or self.board.turn == self.player_color or self.board.is_game_over():
            return
        
        self.thinking = True
        self.thinking_label.config(text="MAIA is thinking...")
        
        def think():
            try:
                # Get MAIA's move
                if self.board.turn == chess.WHITE:
                    top_moves = self.engine.get_best_move(self.board, self.maia_elo, self.player_elo)
                else:
                    top_moves = self.engine.get_best_move(self.board, self.maia_elo, self.player_elo)
                
                if top_moves:
                    best_move_uci = top_moves[0][0]
                    move = chess.Move.from_uci(best_move_uci)
                    
                    # Make the move on the main thread
                    self.root.after(0, lambda: self.execute_maia_move(move))
                else:
                    self.root.after(0, lambda: self.thinking_label.config(text="No legal moves found"))
            except Exception as e:
                self.root.after(0, lambda: self.thinking_label.config(text=f"Error: {e}"))
            finally:
                self.thinking = False
        
        threading.Thread(target=think, daemon=True).start()
    
    def execute_maia_move(self, move):
        """Execute MAIA's move on the main thread."""
        if move in self.board.legal_moves:
            self.chess_board.make_move(move)
            self.game_history.append(move)
            self.update_status()
            self.update_history()
        
        self.thinking_label.config(text="")
        self.thinking = False
    
    def update_status(self):
        """Update game status."""
        if self.board.is_checkmate():
            winner = "Black" if self.board.turn == chess.WHITE else "White"
            self.status_label.config(text=f"Checkmate! {winner} wins!")
        elif self.board.is_stalemate():
            self.status_label.config(text="Stalemate! Draw!")
        elif self.board.is_insufficient_material():
            self.status_label.config(text="Insufficient material! Draw!")
        elif self.board.is_check():
            turn = "White" if self.board.turn == chess.WHITE else "Black"
            self.status_label.config(text=f"{turn} in check!")
        else:
            turn = "White" if self.board.turn == chess.WHITE else "Black"
            self.status_label.config(text=f"{turn} to move")
    
    def update_history(self):
        """Update move history display."""
        self.history_text.delete(1.0, tk.END)
        
        for i, move in enumerate(self.game_history):
            move_num = (i // 2) + 1
            if i % 2 == 0:  # White move
                self.history_text.insert(tk.END, f"{move_num}. {move} ")
            else:  # Black move
                self.history_text.insert(tk.END, f"{move}\n")
        
        self.history_text.see(tk.END)
    
    def save_game(self):
        """Save game to PGN file."""
        if not self.game_history:
            messagebox.showwarning("Warning", "No moves to save!")
            return
        
        filename = filedialog.asksaveasfilename(
            defaultextension=".pgn",
            filetypes=[("PGN files", "*.pgn"), ("All files", "*.*")]
        )
        
        if filename:
            try:
                game = chess.pgn.Game()
                game.headers["Event"] = "MAIA2 Game"
                game.headers["White"] = "Human" if self.player_color == chess.WHITE else f"MAIA2 ({self.maia_elo})"
                game.headers["Black"] = "Human" if self.player_color == chess.BLACK else f"MAIA2 ({self.maia_elo})"
                
                node = game
                board = chess.Board()
                for move in self.game_history:
                    node = node.add_variation(move)
                    board.push(move)
                
                with open(filename, 'w') as f:
                    print(game, file=f)
                
                messagebox.showinfo("Success", f"Game saved to {filename}")
            except Exception as e:
                messagebox.showerror("Error", f"Failed to save game: {e}")
    
    def load_game(self):
        """Load game from PGN file."""
        filename = filedialog.askopenfilename(
            filetypes=[("PGN files", "*.pgn"), ("All files", "*.*")]
        )
        
        if filename:
            try:
                with open(filename, 'r') as f:
                    game = chess.pgn.read_game(f)
                
                if game:
                    self.board = chess.Board()
                    self.game_history = []
                    
                    for move in game.mainline_moves():
                        self.game_history.append(move)
                        self.board.push(move)
                    
                    self.chess_board.set_position(self.board)
                    self.update_status()
                    self.update_history()
                    
                    messagebox.showinfo("Success", f"Game loaded from {filename}")
                else:
                    messagebox.showerror("Error", "No valid game found in file")
            except Exception as e:
                messagebox.showerror("Error", f"Failed to load game: {e}")
    
    def run(self):
        """Run the application."""
        self.root.mainloop()

def main():
    """Main function."""
    print("Starting MAIA2 Chess Game...")
    
    # Check if model exists
    model_path = "maia2_quantized_static.onnx"
    if not os.path.exists(model_path):
        print(f"Error: Model file not found: {model_path}")
        print("Please run create_quantized_onnx.py first to create the quantized model.")
        return
    
    try:
        app = ChessGameGUI()
        app.run()
    except Exception as e:
        print(f"Error starting application: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
